import PropTypes from 'prop-types';
import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { ArrowDown01Icon, Cancel01Icon } from "hugeicons-react";

export default function GeneralSelector({
    id,
    open,
    disabled = false,
    onToggle,
    onChange,
    selectedValue,
    options,
    loading = false,
    placeholder = "Select...",
    onSearchChange,
    onEndScroll,
    useAll = true,
    excludeAll = false, // Add excludeAll prop
    selectMultiple = false, // Add selectMultiple prop
}) {
    const ref = useRef(null);
    const scrollRef = useRef(null);
    const [query, setQuery] = useState("");
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    // Ensure internalSelectedValue is initialized correctly
    const [internalSelectedValue, setInternalSelectedValue] = useState(
        selectMultiple ? (Array.isArray(selectedValue) ? selectedValue : []) : (selectedValue || (useAll ? "All" : ""))
    );

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (ref.current && !ref.current.contains(event.target) && open) {
                onToggle();
                setQuery("");
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [onToggle, open]);

    const handleScroll = (e) => {
        if (!onEndScroll || isLoadingMore) return;

        const { scrollTop, scrollHeight, clientHeight } = e.target;
        if (scrollHeight - scrollTop <= clientHeight + 5) {
            setIsLoadingMore(true);
            onEndScroll()
                .then(() => {
                    setTimeout(() => {
                        setIsLoadingMore(false);
                    }, 300);
                })
                .catch(() => {
                    setIsLoadingMore(false);
                });
        }
    };

    const allOptions = useAll && !excludeAll ? ["All", ...options] : [...options];

    const filteredOptions = allOptions.filter(option =>
        !onSearchChange ? option.toLowerCase().includes(query.toLowerCase()) : true
    );

    const handleOptionClick = (option) => {
        if (selectMultiple) {
            if (option === "All") {
                // Select "All" and unselect other items
                setInternalSelectedValue(["All"]);
                onChange(null); // Pass null to indicate "All" is selected
            } else {
                // Toggle individual option
                const updatedSelection = internalSelectedValue.includes(option)
                    ? internalSelectedValue.filter((val) => val !== option)
                    : [...internalSelectedValue.filter((val) => val !== "All"), option]; // Remove "All" if selecting other items

                setInternalSelectedValue(updatedSelection);
                onChange(updatedSelection);
            }
        } else {

            setInternalSelectedValue(option === "All" ? null : option);
            onChange(option === "All" ? null : option);
            setQuery("");
            onToggle();
        }
    };





    return (
        <div ref={ref} className="relative">
            <button
                type="button"
                className={`${disabled ? "bg-neutral-100" : "bg-white dark:bg-base_dark"
                    } relative w-full border border-gray-300 dark:border-[#ffffff10] rounded-md shadow-sm pl-3 pr-10 py-2.5 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm`}
                aria-haspopup="listbox"
                aria-expanded={open}
                onClick={onToggle}
                disabled={disabled}
            >
                <span className="truncate flex items-center">

                    {selectMultiple &&
                        Array.isArray(internalSelectedValue) &&
                        internalSelectedValue.length > 0 && (
                            <button
                                type="button"
                                className="text-glb_red mr-2"
                                onClick={() => {
                                    setInternalSelectedValue([]);
                                    onChange([]);
                                }}
                            >
                                <Cancel01Icon size={20} />
                            </button>
                        )}
                    {selectedValue && (
                        <button
                            type="button"
                            className="text-glb_red mr-2"
                            onClick={() => {
                                // For multiple select, clear the internal selected value and trigger onChange with an empty array
                                if (Array.isArray(selectedValue)) {
                                    setInternalSelectedValue([]); // Reset internal selected values
                                    onChange([]); // Trigger parent change
                                } else {
                                    // For single select, clear the selected value and trigger onChange with null
                                    onChange(null); // Reset to null for single selection
                                    setInternalSelectedValue(null); // Reset internal selected values
                                }
                            }}
                        >
                            <Cancel01Icon size={20} />
                        </button>
                    )}
                    {loading
                        ? 'Loading...'
                        : selectMultiple
                            ? Array.isArray(internalSelectedValue) && internalSelectedValue.length > 0
                                ? internalSelectedValue.length && internalSelectedValue[0] === 'All' ? 'All' : `${internalSelectedValue.length} selected`
                                : placeholder
                            : (!useAll && internalSelectedValue === "All")
                                ? placeholder
                                : (internalSelectedValue || selectedValue || placeholder)}
                </span>
                {!disabled && (
                    <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <ArrowDown01Icon />
                    </span>
                )}
            </button>

            <AnimatePresence>
                {open && (
                    <motion.ul
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.1 }}
                        className="absolute z-50 mt-1 w-full bg-white dark:bg-base_card shadow-lg max-h-80 rounded-md text-base ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                        tabIndex={-1}
                        role="listbox"
                    >
                        <div className="sticky top-0 z-10 bg-white dark:bg-base_card">
                            <li className="text-gray-900 dark:text-gray-200 cursor-default select-none relative">
                                <input
                                    type="search"
                                    autoComplete="off"
                                    className="focus:ring-blue-500 py-2 px-3 focus:border-gray-500 block w-full sm:text-sm border-gray-300 dark:border-[#ffffff10] rounded-md"
                                    placeholder={'Search...'}
                                    onChange={(e) => { onSearchChange ? onSearchChange(e.target.value) : setQuery(e.target.value); }}
                                />
                            </li>
                            <hr />
                        </div>

                        <div
                            ref={scrollRef}
                            className="max-h-64 overflow-y-scroll scrollbar scrollbar-track-gray-100 scrollbar-thumb-gray-300 hover:scrollbar-thumb-gray-600 scrollbar-thumb-rounded scrollbar-thin"
                            onScroll={onEndScroll ? handleScroll : undefined}
                        >
                            {loading ? (
                                <li className="text-center py-2 text-gray-500 dark:text-gray-300">
                                    Loading...
                                </li>
                            ) : filteredOptions.length === 0 ? (
                                <li className="text-gray-900 dark:text-gray-200 cursor-default select-none relative py-2 pl-3 pr-9">
                                    No results found
                                </li>
                            ) : (
                                <>
                                    {filteredOptions.map((option, index) => {

                                        return (
                                            < li
                                                key={`${index}`}
                                                className={`text-gray-900 dark:text-gray-200 cursor-default select-none relative py-2 pl-3 pr-9 flex items-center hover:bg-gray-50 dark:hover:bg-gray-800 transition ${selectMultiple && internalSelectedValue.includes(option) ? "bg-gray-200 dark:bg-gray-700" : ""}`}
                                                role="option"
                                                onClick={() => handleOptionClick(option)}
                                            >
                                                <span className="truncate">{option}</span>
                                                {(!selectMultiple && option === internalSelectedValue) || (selectMultiple && internalSelectedValue.includes(option)) && (
                                                    <span className="text-blue-600 absolute inset-y-0 right-0 flex items-center pr-8">
                                                        <svg
                                                            className="h-5 w-5"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                        >
                                                            <path
                                                                fillRule="evenodd"
                                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                clipRule="evenodd"
                                                            />
                                                        </svg>
                                                    </span>
                                                )}
                                                {(!selectMultiple && option === selectedValue) &&
                                                    (<span className="text-blue-600 absolute inset-y-0 right-0 flex items-center pr-8">
                                                        <svg
                                                            className="h-5 w-5"
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            fill="currentColor"
                                                            viewBox="0 0 20 20"
                                                        >
                                                            <path
                                                                fillRule="evenodd"
                                                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                                                clipRule="evenodd"
                                                            />
                                                        </svg>
                                                    </span>)
                                                }
                                            </li>
                                        )
                                    })}
                                    {isLoadingMore && (
                                        <li className="text-center py-2 text-gray-500 dark:text-gray-300">
                                            ...loading
                                        </li>
                                    )}
                                </>
                            )}
                        </div>
                    </motion.ul>
                )}
            </AnimatePresence>
        </div >
    );
}

GeneralSelector.propTypes = {
    id: PropTypes.string.isRequired,
    open: PropTypes.bool.isRequired,
    disabled: PropTypes.bool,
    onToggle: PropTypes.func.isRequired,
    onChange: PropTypes.func.isRequired,
    selectedValue: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.arrayOf(PropTypes.string), // Add array type for multiple selection
        PropTypes.shape({
            value: PropTypes.string,
            title: PropTypes.string,
        })
    ]),
    options: PropTypes.arrayOf(
        PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.shape({
                value: PropTypes.string.isRequired,
                title: PropTypes.string.isRequired,
            })
        ])
    ).isRequired,
    loading: PropTypes.bool,
    placeholder: PropTypes.string,
    useIcon: PropTypes.bool,
    getImageSrc: PropTypes.func,
    onSearchChange: PropTypes.func,
    onEndScroll: PropTypes.func,
    useAll: PropTypes.bool,
    excludeAll: PropTypes.bool, // Add excludeAll prop type
    selectMultiple: PropTypes.bool, // Add selectMultiple prop type
};
